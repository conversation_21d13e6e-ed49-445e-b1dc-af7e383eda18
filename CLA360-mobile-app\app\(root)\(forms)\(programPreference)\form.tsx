import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import InputField from "../../../../components/InputField";
import { SafeAreaView } from "react-native-safe-area-context";
import Header from "@/components/header";
import { router } from "expo-router";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { Countries, enums } from "@/constants";
import FormLayout from "@/components/formLayout";
import SelectDropDown from "@/components/selectDropDown";
import { Picker } from "@react-native-picker/picker";

const ProgramPreference = () => {
  const [programPreference, setProgramPreference] = useState({
    intendedFieldOfStudy: "",
    secondaryFieldOfStudy: "",
    startTerm: "",
    startYear: "",
    preferedCountry: "",
    
    
  });
  const preferredLearningStyle = enums.preferedLearningStyle;
  const preferredStartTerm = enums.preferredStartTerm;

  return (
    <FormLayout title="Program Preference">
    <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300"/>
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Program Preference
        </Text>
          <View className="flex-1 h-[2px] bg-gray-300"/>
        </View>
        <InputField
          label="Intended Field of Study"
          value={programPreference.intendedFieldOfStudy}
          onChangeText={(value) =>
            setProgramPreference({ ...programPreference, intendedFieldOfStudy: value })
          }
        />
        <InputField
          label="Secondary Field of Study"
          value={programPreference.secondaryFieldOfStudy}
          onChangeText={(value) =>
            setProgramPreference({ ...programPreference, secondaryFieldOfStudy: value })
          }
        />
        <SelectDropDown label="Preferred Start Term">
        <Picker
          style={{ flex: 1 }}
          selectedValue={programPreference.startTerm}
          onValueChange={(itemValue, itemIndex) =>
            setProgramPreference({ ...programPreference, startTerm: itemValue })
          }
        >
          {preferredStartTerm?.map((term, index) => (
            <Picker.Item label={term} value={term} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
        <InputField
            label="Preferred Start Year"
            value={programPreference.startYear}
            onChangeText={(value) =>
            setProgramPreference({ ...programPreference, startYear: value })
            }
        />
        <SelectDropDown label="Preferred Country">
        <Picker
          style={{ flex: 1 }}
          selectedValue={programPreference.preferedCountry}
          onValueChange={(itemValue, itemIndex) =>
            setProgramPreference({ ...programPreference, preferedCountry: itemValue })
          }
        >
          {Countries?.map((country) => (
            <Picker.Item
              label={country.name}
              value={country.id}
              key={country.id}
            />
          ))}
        </Picker>
      </SelectDropDown>
        
        
        <View className="flex flex-row gap-3 mt-5">
          <TouchableOpacity
            onPress={() => {router.back()}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-gray-400 mt-3"
          >
            
            <Text className="font-JakartaSemiBold text-white">
              Previous
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {router.push("/(root)/(forms)/(financialInformation)/form")}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">
              Next
            </Text>
          </TouchableOpacity>
          
        </View>
    </FormLayout>
  );
};

export default ProgramPreference;
