import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import InputField from "../../../../components/InputField";
import { router } from "expo-router";
import { Countries, enums } from "@/constants";
import FormLayout from "@/components/formLayout";
import DatePicker from "@/components/datePicker";
import SelectDropDown from "@/components/selectDropDown";
import { Picker } from "@react-native-picker/picker";

const EducationalDetails = () => {
  const [educationData, setEducationData] = useState({
    highSchoolName: "",
    graduationDate: new Date(1598051730000),
    universityName: "",
    courseOffered: "",
    qualification: "",
    endDate: new Date(1598051730000),
    
  });
  

  return (
    <FormLayout title="Educational Details">
    <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300"/>
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          High School
        </Text>
          <View className="flex-1 h-[2px] bg-gray-300"/>
        </View>
        <InputField
          label="Name"
          placeholder="Springfield High School"
          value={educationData.highSchoolName}
          onChangeText={(value) =>
            setEducationData({ ...educationData, highSchoolName: value })
          }
        />
        <DatePicker
        value={educationData.graduationDate}
        required
        label="Graduation Date"
        onChange={(value: any) =>
          setEducationData({ ...educationData, graduationDate: value })
        }
      />
        
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300"/>
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Previous University
        </Text>
          <View className="flex-1 h-[2px] bg-gray-300"/>
        </View>
        
        <InputField
          label="Name"
          placeholder="State University"
          value={educationData.universityName}
          onChangeText={(value) =>
            setEducationData({ ...educationData, universityName: value })
          }
        />
          <InputField
            label="Course Offered"
            placeholder="Computer Science"
            value={educationData.courseOffered}
            onChangeText={(value) =>
              setEducationData({ ...educationData, courseOffered: value })
            }
          />
          <SelectDropDown label="Qualification">
        <Picker
          style={{ flex: 1 }}
          selectedValue={educationData.qualification}
          onValueChange={(itemValue, itemIndex) =>
            setEducationData({ ...educationData, qualification: itemValue })
          }
        >
          {enums.academicBackground?.map((level, index) => (
            <Picker.Item label={level} value={level} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
      <DatePicker
        value={educationData.endDate}
        required
        label="Graduation Date"
        onChange={(value: any) =>
          setEducationData({ ...educationData, endDate: value })
        }
      />
        <View className="flex flex-row gap-3 mt-5">
          <TouchableOpacity
            onPress={() => {router.back()}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-gray-400 mt-3"
          >
            
            <Text className="font-JakartaSemiBold text-white">
              Previous
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {router.push("/(root)/(forms)/(programPreference)/form")}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
          >
            
            <Text className="font-JakartaSemiBold text-white">
              Next
            </Text>
          </TouchableOpacity>
          
        </View>
        </FormLayout>
  );
};

export default EducationalDetails;
