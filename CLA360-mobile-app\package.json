{"name": "cla360-mobile-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@clerk/clerk-expo": "^2.4.1", "@expo/metro-runtime": "~4.0.0", "@expo/vector-icons": "^14.0.2", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/drawer": "^7.0.19", "@react-navigation/native": "^7.0.0", "@stripe/stripe-react-native": "^0.41.0", "expo": "~52.0.11", "expo-blur": "~14.0.1", "expo-checkbox": "~4.0.1", "expo-constants": "~17.0.3", "expo-document-picker": "^13.0.3", "expo-font": "~13.0.1", "expo-haptics": "~14.0.0", "expo-image-picker": "~16.0.3", "expo-linear-gradient": "^14.0.1", "expo-linking": "~7.0.3", "expo-local-authentication": "^15.0.1", "expo-router": "~4.0.9", "expo-secure-store": "^14.0.0", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.4", "expo-web-browser": "~14.0.1", "formik": "^2.4.6", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.3", "react-native-animatable": "^1.4.0", "react-native-bouncy-checkbox": "^4.1.2", "react-native-country-picker-modal": "^2.0.0", "react-native-element-dropdown": "^2.12.2", "react-native-gesture-handler": "~2.20.2", "react-native-heroicons": "^4.0.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^13.0.1", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-select-dropdown": "^4.0.1", "react-native-swiper": "^1.6.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.2", "stripe": "^17.6.0", "tailwindcss": "^3.4.16", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.2", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}