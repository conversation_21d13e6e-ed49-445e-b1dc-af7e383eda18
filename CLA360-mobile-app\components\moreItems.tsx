import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { ButtonProps } from '@/types/type'
import { ChevronRightIcon } from 'react-native-heroicons/outline'

const MoreItem = ({onPress, title, bgVariant="success", textVariant="default", IconLeft, IconRight, className, ...props} : ButtonProps) => {
  return (
    <TouchableOpacity onPress={onPress}>
        <View className="w-full px-5 py-2 flex items-center justify-between flex-row">
            {IconLeft && <IconLeft/>}
            <Text className='text-gray-600 font-JakartaSemiBold text-lg'>{title}</Text>
        {IconRight && <ChevronRightIcon color="black" className='text-gray-600'/>}
        </View>
    </TouchableOpacity>
  )
}

export default MoreItem