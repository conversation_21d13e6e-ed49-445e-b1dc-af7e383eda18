import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import { University } from "@/types/type";
import { Universities, icons, images } from "@/constants";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";

const UniversityCard = ({ university }: { university: University }) => {
  return (
    <View className="w-full px-5 my-3">
      <LinearGradient
        colors={["#ffffff", "#4287f5"]}
        start={{ x: 0.5, y: 0.8 }}
        end={{ x: 2, y: -1.5 }}
      >
        
        <TouchableOpacity
          onPress={() => {
            router.push('/(root)/(universities)/[id]')
          }}
        >
          <View className="flex flex-row gap-5 justify-start items-center px-5 rounded-md ">
            <View className="w-1/5 flex flex-col justify-center items-center py-2">
              <Image
                source={university.logo ? university.logo  : images.university}
                resizeMode="contain"
                className="w-[90px] h-[90px] m-0"
              />
              <Text className="text-gray-500 font-JakartaSemiBold p-0 m-0">
                {university.abbr}
              </Text>
            </View>
            <View className="w-4/5 h-full py-5 overflow-clip">
              <Text className="text-md font-JakartaBold overflow-clip">
                {university.name}
              </Text>
              <View className="my-2 flex flex-col gap-1">
              <View className="flex flex-row gap-3 justify-start items-center">
                <Image
                  source={icons.point}
                  className="w-[20px] h-[20px]"
                  resizeMode="contain"
                  tintColor="#88909e"
                />
                <Text className="text-gray-400 font-JakartaSemiBold">
                  {university.location}
                </Text>
              </View>
              <View className="my-1 flex flex-row gap-3 justify-start items-center">
                <Image
                  source={icons.email}
                  className="w-[20px] h-[20px]"
                  resizeMode="contain"
                  tintColor="#88909e"
                />
                <Text className="text-gray-400 font-JakartaSemiBold">
                  {university.email}
                </Text>
              </View>
              </View>
              <View className="w-full h-[2px] bg-gray-300" />
              <Text className="text-blue-400 font-JakartaSemiBold mt-2">
              Compatibility: <Text className="text-gray-700 font-JakartaSemiBold">
  {university.compatibility ? `${university.compatibility}%` : "N/A"}
</Text>
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
};

export default UniversityCard;
