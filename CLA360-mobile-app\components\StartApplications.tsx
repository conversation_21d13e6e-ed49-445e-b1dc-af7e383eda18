import { StyleSheet, Text, TouchableOpacity, View, Image } from 'react-native'
import React from 'react'
import { images } from '@/constants'
import { router } from 'expo-router'

const StartApplications = () => {
  return (
    <TouchableOpacity
    onPress={() => {
      router.push("/(root)/(tabs)/schools")
    }}
    >
        <View className='flex flex-row rounded-2xl p-5 justify-start items-center bg-green-100 mt-5 gap-3'>
          <Image source={images.applications} className='w-[100px] h-[100px]' resizeMode='contain'/>
          <View className=''>
            <Text className="text-2xl font-JakartaBold text-blue-600">Applications</Text>
            <Text className="text-md font-JakartaSemiBold text-gray-600">View All Applications</Text>
          </View>
        </View>
    </TouchableOpacity>
  )
}

export default StartApplications

const styles = StyleSheet.create({})