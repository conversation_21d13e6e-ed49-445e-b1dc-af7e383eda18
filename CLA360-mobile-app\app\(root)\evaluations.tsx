import { FlatList, StyleSheet, Text, TouchableOpacity, View, Image } from 'react-native'
import React, { useState } from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import Header from '@/components/header'
import UniversityCard from '@/components/UniversityCard'
import { icons, images } from '@/constants'
import ReactNativeModal from 'react-native-modal'

const Evaluations = () => {
  const [showDetails, setShowDetails] = useState(false)
  return (
    <SafeAreaView className="h-screen">
      <Header title="Evaluations"/>
      {/* <View>
      <TouchableOpacity
          onPress={() => {
            setShowDetails(true)
          }}
        >
          <View className='flex flex-row items-center gap-2 bg-red-200 rounded-full p-3'>
            <Image source={icons.add} className="w-7 h-7" resizeMode='contain'/>
            <Text className='font-JakartaSemiBold text-gray-600'>Request Transcript</Text>
          </View>
        </TouchableOpacity>
      </View> */}
      <FlatList
        data={[]}
        // keyExtractor={(item) => item.id}
        renderItem={({ item }) => <UniversityCard university={item} />}
        ListEmptyComponent={
          <View className="flex flex-col items-center justify-center gap-3">
            <Image source={images.noResult} className="w-[150px] h-[150px]" resizeMode="contain"/>
            <View className="justify-center items-center gap-3">
              <Text className="text-lg text-gray-600 font-JakartaBold">No Evaluations Available</Text>
              <Text className="text-md text-gray-600 font-JakartaSemiBold">Complete Profile to View Evaluations</Text>
            </View>
          </View>
        }
      />
      <ReactNativeModal
          isVisible={showDetails}
          onModalHide={() => {}}
        >
          <View className="bg-white px-7 py-9 rounded-2xl min-h-[300px]">
            <View className='flex flex-row justify-between items-center'>
              <Text className='text-md font-JakartaSemiBold text-gray-700'>Trascript Request</Text>
              <TouchableOpacity onPress={() => {
                setShowDetails(false)
              }}>
                <Image source={icons.close} resizeMode='contain' className='w-[20px] h-[20px]'/>
              </TouchableOpacity>
            </View>
            
          </View>
        </ReactNativeModal>
    </SafeAreaView>
  )
}

export default Evaluations