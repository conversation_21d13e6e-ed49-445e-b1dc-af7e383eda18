import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { CheckCircleIcon, CheckIcon } from "react-native-heroicons/outline";

type RadioProps = {
  label: string;
  onChange: any;
  checkedValue: boolean;
  required?: boolean;
};

const Radio = ({label, onChange, checkedValue, required=true, ...props}: RadioProps) => {
  return (
    <View className={`my-2 w-full `}>
        <Text className={`text-lg font-JakartaSemiBold mb-3 `}>
          {label}{required ? <Text className="text-red-700">*</Text> : ""}
        </Text>
        <View
          className={`flex flex-row justify-between items-center relative rounded-md bg-neutral-200 border border-neutral-100 focus:border-primary-500 mt-3`}
        >
            <TouchableOpacity onPress={onChange} className={`h-[40px] flex-1 items-center border border-blue-400 ${checkedValue ? "bg-blue-300" : ""} rounded-md`}  {...props}>
            <View className="w-full h-full items-center justify-center flex flex-row gap-2">
              {checkedValue ? <CheckCircleIcon color="white"/> : ""}
              <Text className="font-JakartaSemiBold">{checkedValue ? "Yes" : "No"}</Text>
            </View>
          </TouchableOpacity>
    </View>
      </View>
  );
};

export default Radio;
