import { View, Text, TouchableWithoutFeedback, Keyboard } from 'react-native'
import React from 'react'
import { Picker } from '@react-native-picker/picker';

type SelectDropdownProps = {
    label: string;
    required?: boolean;
    children: React.ReactNode;
  };

const SelectDropDown = ({label, required=true, children}:SelectDropdownProps) => {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className={`my-2 w-full `}>
        <Text className={`text-lg font-JakartaSemiBold mb-3 `}>
          {label}{required ? <Text className="text-red-700">*</Text> : ""}
        </Text>
        <View
          className={`flex flex-row justify-start items-center relative rounded-md bg-neutral-200 border border-neutral-100 focus:border-primary-500 mt-3`}
        >
          
            {children}
        </View>
      </View>
    </TouchableWithoutFeedback>
  )
}

export default SelectDropDown