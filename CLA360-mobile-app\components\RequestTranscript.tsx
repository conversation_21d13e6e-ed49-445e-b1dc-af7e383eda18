import { StyleSheet, Text, TouchableOpacity, View, Image } from 'react-native'
import React from 'react'
import { LinearGradient } from 'expo-linear-gradient'
import { images } from '@/constants'
import { router } from 'expo-router'

const RequestTranscript = () => {
  return (
    <TouchableOpacity
    onPress={() => {
      router.push("/(root)/transcripts")
    }}
    >
        <View className='flex flex-row rounded-2xl p-5 justify-start items-center bg-red-100 mt-5 gap-3'>
          <Image source={images.transcript1} className='w-[100px] h-[100px]' resizeMode='contain'/>
          <View className=''>
            <Text className="text-2xl font-JakartaBold text-blue-600">Transcripts</Text>
            <Text className="text-md font-JakartaSemiBold text-gray-600">Request and View transcripts</Text>
          </View>
        </View>
    </TouchableOpacity>
  )
}

export default RequestTranscript

const styles = StyleSheet.create({})