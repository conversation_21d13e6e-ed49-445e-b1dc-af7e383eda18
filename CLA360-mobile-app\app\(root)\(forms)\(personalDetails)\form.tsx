import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import FormLayout from "@/components/formLayout";
import { router } from "expo-router";
import InputField from "@/components/InputField";
import { Countries } from "@/constants";
import SelectDropDown from "@/components/selectDropDown";
import { Picker } from "@react-native-picker/picker";
import DatePicker from "@/components/datePicker";

const PersonalDetails = () => {
  const [personalData, setPersonalData] = useState({
    countryOfBirth: "",
    stateOfBirth: "",
    cityOfBirth: "",
    dateOfBirth: new Date(),
    gender: "",
    maritalStatus: "",
    nationality: "",
    primaryPhone: "",
    secondaryPhone: "",
    primaryAddress: {
      line_1: "",
      line_2: "",
      line_3: "",
      country_id: "",
      state: "",
      city: "",
      zipCode: "",
    },
  });
  const gender = ["Male", "Female", "Others"];
  const maritalStatus = ["Single", "Married", "Divorced"];

  return (
    <FormLayout title="Personal Details">
      <View className="flex flex-row items-center justify-center gap-2 mt-2">
        <View className="flex-1 h-[2px] bg-gray-300" />
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Bio Data
        </Text>
        <View className="flex-1 h-[2px] bg-gray-300" />
      </View>

      <DatePicker
        value={personalData.dateOfBirth}
        required
        label="Date of Birth"
        onChange={(value: any) =>
          setPersonalData({ ...personalData, dateOfBirth: value })
        }
      />
      <SelectDropDown label="Gender">
        <Picker
          style={{ flex: 1 }}
          selectedValue={personalData.gender}
          onValueChange={(itemValue, itemIndex) =>
            setPersonalData({ ...personalData, gender: itemValue })
          }
        >
          {gender?.map((status, index) => (
            <Picker.Item label={status} value={status} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
      
      <SelectDropDown
        label="Country of Birth"
      >
        <Picker style={{ flex: 1 }}
        selectedValue={personalData.countryOfBirth}
          onValueChange={(itemValue, itemIndex) =>
            setPersonalData({ ...personalData, countryOfBirth: itemValue })
          }
        >
          {Countries?.map((country) => (
            <Picker.Item
              label={country.name}
              value={country.id}
              key={country.id}
            />
          ))}
        </Picker>
      </SelectDropDown>
      <InputField
        label="State of Birth"
        containerStyle="rounded-md"
        value={personalData.stateOfBirth}
        onChangeText={(value) =>
          setPersonalData({ ...personalData, stateOfBirth: value })
        }
      />
      <InputField
        label="City of Birth"
        containerStyle="rounded-md"
        value={personalData.cityOfBirth}
        onChangeText={(value) =>
          setPersonalData({ ...personalData, cityOfBirth: value })
        }
      />
      <SelectDropDown label="Marital Status">
        <Picker
          style={{ flex: 1 }}
          selectedValue={personalData.maritalStatus}
          onValueChange={(itemValue, itemIndex) =>
            setPersonalData({ ...personalData, maritalStatus: itemValue })
          }
        >
          {maritalStatus?.map((status, index) => (
            <Picker.Item label={status} value={status} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
      <SelectDropDown label="Nationality">
        <Picker style={{ flex: 1 }} 
          onValueChange={(itemValue, itemIndex) =>
            setPersonalData({ ...personalData, nationality: itemValue })
          }
          selectedValue={personalData.nationality}
        >
          {Countries?.map((country) => (
            <Picker.Item
              label={country.nationality}
              value={country.id}
              key={country.id}
            />
          ))}
        </Picker>
      </SelectDropDown>
      <View className="flex flex-row items-center justify-center gap-2 mt-2">
        <View className="flex-1 h-[2px] bg-gray-300" />
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Address Data
        </Text>
        <View className="flex-1 h-[2px] bg-gray-300" />
      </View>
      <InputField
        label="Address Line 1"
        placeholder="123 Main Street"
        value={personalData.primaryAddress.line_1}
        onChangeText={(value) =>
          setPersonalData({
            ...personalData,
            primaryAddress: {
              ...personalData.primaryAddress,
              line_1: value,
            },
          })
        }
      />
      <InputField
        label="Address Line 2"
        placeholder="123 Main Street"
        value={personalData.primaryAddress.line_2}
        onChangeText={(value) =>
          setPersonalData({
            ...personalData,
            primaryAddress: {
              ...personalData.primaryAddress,
              line_2: value,
            },
          })
        }
      />
      <InputField
        label="Address Line 3"
        placeholder="123 Main Street"
        value={personalData.primaryAddress.line_3}
        onChangeText={(value) =>
          setPersonalData({
            ...personalData,
            primaryAddress: {
              ...personalData.primaryAddress,
              line_3: value,
            },
          })
        }
      />
      <SelectDropDown label="Country"
      >
        <Picker style={{ flex: 1 }}
        selectedValue={personalData.primaryAddress.country_id}
          onValueChange={(itemValue, itemIndex) =>
            setPersonalData({
              ...personalData,
              primaryAddress: {
                ...personalData.primaryAddress,
                country_id: itemValue,
              },
            })
          }
        >
          {Countries?.map((country) => (
            <Picker.Item
              label={country.name}
              value={country.id}
              key={country.id}
            />
          ))}
        </Picker>
      </SelectDropDown>
      <InputField
        label="State"
        placeholder=""
        value={personalData.primaryAddress.state}
        onChangeText={(value) =>
          setPersonalData({
            ...personalData,
            primaryAddress: {
              ...personalData.primaryAddress,
              state: value,
            },
          })
        }
      />
      <InputField
        label="City"
        placeholder=""
        value={personalData.primaryAddress.city}
        onChangeText={(value) =>
          setPersonalData({
            ...personalData,
            primaryAddress: { ...personalData.primaryAddress, city: value },
          })
        }
      />
      <InputField
        label="Zip Code"
        placeholder=""
        value={personalData.primaryAddress.zipCode}
        onChangeText={(value) =>
          setPersonalData({
            ...personalData,
            primaryAddress: {
              ...personalData.primaryAddress,
              zipCode: value,
            },
          })
        }
      />
      <View className="flex flex-row gap-3">
        <TouchableOpacity
          onPress={() => {
            router.push("/(root)/(forms)/(gaurdianDetails)/form");
          }}
          className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
        >
          <Text className="font-JakartaSemiBold text-white">Next</Text>
        </TouchableOpacity>
      </View>
    </FormLayout>
  );
};

export default PersonalDetails;
