import { StyleSheet, Text, TouchableOpacity, View, Image } from 'react-native'
import React from 'react'
import { images } from '@/constants'
import { router } from 'expo-router'

const RequestEvaluation = () => {
  return (
    <TouchableOpacity
      onPress={() => {
        router.push("/(root)/evaluations")
      }}
    >
        <View className='flex flex-row rounded-2xl p-5 justify-start items-center bg-blue-100 mt-5 gap-3'>
          <View className=''>
            <Text className="text-2xl font-JakartaBold text-blue-600">Evaluations</Text>
            <Text className="text-md font-JakartaSemiBold text-gray-600">See what CLA360 has to say </Text>
          </View>
          <Image source={images.transcript} className='w-[100px] h-[100px]' resizeMode='contain'/>
        </View>
    </TouchableOpacity>
  )
}

export default RequestEvaluation

const styles = StyleSheet.create({})