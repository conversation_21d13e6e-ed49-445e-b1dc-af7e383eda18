import { Stack, router } from 'expo-router';
import {GestureHandlerRootView} from 'react-native-gesture-handler'
import {Drawer} from 'expo-router/drawer'
import { TouchableOpacity, View } from 'react-native';
import { PencilSquareIcon } from 'react-native-heroicons/outline';


const Layout = () => {

  return (
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="(forms)" options={{ headerShown: false }} />
        <Stack.Screen name="settings" options={{ headerShown: false }} />
        <Stack.Screen name="notification" options={{ headerShown: false }} />
        <Stack.Screen name="transcripts" options={{ headerShown: false }} />
        <Stack.Screen name="evaluations" options={{ headerShown: false }} />
        <Stack.Screen name="payment" options={{ title: "Payment", headerBackTitle: "Back" }} />
        <Stack.Screen name="profile" options={{ title: "Profile", headerBackTitle: "Back", headerRight: () => {return <TouchableOpacity onPress={() => {
          router.push("/(root)/profileUpdate")
        }}>
          <PencilSquareIcon color="black"/>
        </TouchableOpacity>;} }} />
        <Stack.Screen name="(universities)" options={{ headerShown: false }} />
        <Stack.Screen name="profileUpdate" options={{ title: "Edit Profile", headerBackTitle: "Back" }} />
      </Stack>
  );
}


export default Layout;