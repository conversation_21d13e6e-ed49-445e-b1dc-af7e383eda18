import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import InputField from "../../../../components/InputField";
import { Picker } from "@react-native-picker/picker";
import { router } from "expo-router";
import { Countries, enums } from "@/constants";
import FormLayout from "@/components/formLayout";
import SelectDropDown from "@/components/selectDropDown";

const GaurdianInformation = () => {

  const [gaurdianData, setGuardianData] = useState({
    fullName: "",
    phone: "",
    relation : "",
    email: "",
    occupation: "",
    address: {
      line_1: "",
      line_2: "",
      line_3: "",
      country_id: "",
      state: "",
      city: "",
      zipCode: "",
    },
    highEducationLevel: "",
    institution: "",
  });

  const relation = enums.gaurdianRelation
  const educationLevel = enums.gaurdianHighestEducationLevel
  return (
    <FormLayout title="Gaurdian Details">
    <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300"/>
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Gaurdian Details
        </Text>
          <View className="flex-1 h-[2px] bg-gray-300"/>
        </View>
        
        <InputField
          label="Full Name"
          placeholder="Full Name"
          value={gaurdianData.fullName}
          onChangeText={(value) =>
            setGuardianData({ ...gaurdianData, fullName: value })
          }
        />
        <SelectDropDown label="Relation">
        <Picker
          style={{ flex: 1 }}
          selectedValue={gaurdianData.relation}
          onValueChange={(itemValue, itemIndex) =>
            setGuardianData({ ...gaurdianData, relation: itemValue })
          }
        >
          {relation?.map((status, index) => (
            <Picker.Item label={status} value={status} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
        {/* <InputField
          label="Occupation"
          placeholder="Occupation"
          value={gaurdianData.occupation}
          onChangeText={(value) =>
            setGuardianData({ ...gaurdianData, occupation: value })
          }
        /> */}
        <InputField
          label="Email"
          placeholder="Email"
          value={gaurdianData.email}
          onChangeText={(value) =>
            setGuardianData({ ...gaurdianData, email: value })
          }
        />
        <InputField
          label="Phone"
          type="phone"
          placeholder=""
          value={gaurdianData.fullName}
          onChangeText={(value) =>
            setGuardianData({ ...gaurdianData, fullName: value })
          }
        />
         <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300"/>
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Address
        </Text>
          <View className="flex-1 h-[2px] bg-gray-300"/>
        </View>
          <InputField
            label="Address Line 1"
            placeholder="123 Main Street"
            value={gaurdianData.address.line_1}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: {
                  ...gaurdianData.address,
                  line_1: value,
                },
              })
            }
          />
          <InputField
            label="Address Line 2"
            placeholder="123 Main Street"
            value={gaurdianData.address.line_2}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: {
                  ...gaurdianData.address,
                  line_2: value,
                },
              })
            }
          />
          <InputField
            label="Address Line 3"
            placeholder="123 Main Street"
            value={gaurdianData.address.line_3}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: {
                  ...gaurdianData.address,
                  line_3: value,
                },
              })
            }
          />
          <SelectDropDown label="Country">
        <Picker
          style={{ flex: 1 }}
          selectedValue={gaurdianData.address.country_id}
          onValueChange={(itemValue, itemIndex) =>
            setGuardianData({
              ...gaurdianData,
              address: {
                ...gaurdianData.address,
                country_id: itemValue,
              },
            })
          }
        >
          {Countries?.map((country) => (
            <Picker.Item
              label={country.name}
              value={country.id}
              key={country.id}
            />
          ))}
        </Picker>
      </SelectDropDown>
          <InputField
            label="State"
            placeholder=""
            value={gaurdianData.address.state}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: {
                  ...gaurdianData.address,
                  state: value,
                },
              })
            }
          />
          <InputField
            label="City"
            placeholder=""
            value={gaurdianData.address.city}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: { ...gaurdianData.address, city: value },
              })
            }
          />
          <InputField
            label="Zip Code"
            placeholder=""
            value={gaurdianData.address.zipCode}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: {
                  ...gaurdianData.address,
                  zipCode: value,
                },
              })
            }
          />
          <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300"/>
        <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
          Educational Background
        </Text>
          <View className="flex-1 h-[2px] bg-gray-300"/>
        </View>
        <SelectDropDown label="Highest Education Level">
        <Picker
          style={{ flex: 1 }}
          selectedValue={gaurdianData.highEducationLevel}
          onValueChange={(itemValue, itemIndex) =>
            setGuardianData({
              ...gaurdianData,
              address: {
                ...gaurdianData.address,
                zipCode: itemValue,
              },
            })
          }
        >
          {educationLevel?.map((level, index) => (
            <Picker.Item label={level} value={level} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
       
          
        <InputField
            label="Institution"
            placeholder=""
            value={gaurdianData.address.zipCode}
            onChangeText={(value) =>
              setGuardianData({
                ...gaurdianData,
                address: {
                  ...gaurdianData.address,
                  zipCode: value,
                },
              })
            }
          />
        <View className="flex flex-row gap-3 mt-5">
          <TouchableOpacity
            onPress={() => {router.back()}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-gray-400 mt-3"
          >
            
            <Text className="font-JakartaSemiBold text-white">
              Previous
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              router.push("/(root)/(forms)/(educationalDetails)/form")
            }}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
          >
            
            <Text className="font-JakartaSemiBold text-white">
              Next
            </Text>
          </TouchableOpacity>
          
        </View>
    </FormLayout>
  );
};

export default GaurdianInformation;
