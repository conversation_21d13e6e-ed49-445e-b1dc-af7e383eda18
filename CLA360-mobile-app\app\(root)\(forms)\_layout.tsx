import { Stack } from 'expo-router';


const Layout = () => {

  return (
      <Stack>
        <Stack.Screen name="(transcriptUpload)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(personalDetails)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(gaurdianDetails)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(educationalDetails)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(programPreference)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(financialInformation)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(extracurricularActivities)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(workExperience)/form" options={{ headerShown: false }} />
        <Stack.Screen name="(additionalInformation)/form" options={{ headerShown: false }} />
        
      </Stack>
  );
}


export default Layout;