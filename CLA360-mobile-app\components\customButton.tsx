import { View, Text, TouchableOpacity } from 'react-native'
import React from 'react'
import { ButtonProps } from '@/types/type'

const getBgVariantStyle = (variant:ButtonProps['bgVariant']) => {
    switch (variant){
        case 'secondary':
            return "bg-gray-400";
        case 'danger':
            return "bg-red-500";
        case 'success':
            return "bg-blue-400";
        case 'outline':
            return "bg-transparent border-neutral-300 border-[0.5px]";
            default:
                return "bg-[#0286ff]"
    }
}
const getTextStyle = (variant:ButtonProps['textVariant']) => {
    switch (variant){
        case 'primary':
            return "text-black";
        case 'secondary':
            return "text-gray-100";
        case 'danger':
            return "bg-red-100";
        case 'success':
            return "bg-green-100";
        
            default:
                return "text-white"
    }
}

const CustomButton = ({onPress, title, bgVariant="primary", textVariant="default", IconLeft, IconRight, className, ...props} : ButtonProps) => {
  return (
    <TouchableOpacity
        onPress={onPress}
        className={`p-3 rounded-md w-full mt-5 flex-row justify-center items-center shadow-md shadow-neutral-400/70 ${getBgVariantStyle(bgVariant)} ${className}`}
        {...props}
    >
      {IconLeft && <IconLeft/>}
      <Text className={`text-lg font-bold $getTextVariantStyle(textVariant)`}>{title}</Text>
    </TouchableOpacity>
  )
}

export default CustomButton