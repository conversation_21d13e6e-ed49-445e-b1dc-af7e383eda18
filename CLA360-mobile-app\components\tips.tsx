import { View, Text } from 'react-native'
import React from 'react'
import Checkbox from "expo-checkbox";

type TipProps = {
    title: string;
    checked: boolean;
}

const Tips = (props: TipProps) => {
  return (
    <View className="flex flex-row gap-2 items-center overflow-ellipsis">
            <Checkbox
              value={props.checked}
              color="#22c55e"
              disabled
            />
            <Text className="font-JakartaSemiBold text-lg">
              {props.title}
            </Text>
          </View>
  )
}

export default Tips