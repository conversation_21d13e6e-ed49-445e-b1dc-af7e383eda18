import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import InputField from "@/components/InputField";
import { router } from "expo-router";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import FormLayout from "@/components/formLayout";
import DatePicker from "@/components/datePicker";

const WorkExperience = () => {
  const [workExperience, setWorkExperience] = useState({
    company: "",
    position: "",
    summaryofResponsibilities: "",
    start: new Date(),
    end: new Date(),
  });

  const [showSponsor, setShowSponsor] = useState(true);

  return (
    <FormLayout title="Work Experience">
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Previous Work Experience
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <InputField
          label="Company"
          value={workExperience.company}
          onChangeText={(value) =>
            setWorkExperience({
              ...workExperience,
              company: value,
            })
          }
        />
        <InputField
          label="Position"
          value={workExperience.position}
          onChangeText={(value) =>
            setWorkExperience({
              ...workExperience,
              position: value,
            })
          }
        />
        <InputField
          label="Summary of Responsibilities"
          value={workExperience.summaryofResponsibilities}
          onChangeText={(value) =>
            setWorkExperience({
              ...workExperience,
              summaryofResponsibilities: value,
            })
          }
        />
        <DatePicker
        value={workExperience.start}
        required
        label="Start Date"
        onChange={(value: any) =>
                setWorkExperience({
                  ...workExperience,
                  start: value,
                })
        }
      />
      <DatePicker
        value={workExperience.end}
        required
        label="End Date"
        onChange={(value: any) =>
          setWorkExperience({
                  ...workExperience,
                  end: value,
                })
        }
      />

        

        <View className="flex flex-row gap-3 mt-5">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-gray-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">Previous</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {router.push("/(root)/(forms)/(additionalInformation)/form")}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">Next</Text>
          </TouchableOpacity>
        </View>
    </FormLayout>
  );
};

export default WorkExperience;
