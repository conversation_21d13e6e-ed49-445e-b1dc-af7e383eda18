import { View, Text } from 'react-native'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import Header from './header';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

const FormLayout = ({title, children}: {title: string; children:React.ReactNode}) => {
  return (
    <SafeAreaView className="h-screen bg-white px-5 pb-5">
      <Header title={title || "Apply"}/>
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
      {children}
      </KeyboardAwareScrollView>
    </SafeAreaView>
  )
}

export default FormLayout