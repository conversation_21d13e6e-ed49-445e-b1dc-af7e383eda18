import { View, Text, Pressable, TextInput } from "react-native";
import React, { useState } from "react";
import RNDateTimePicker from "@react-native-community/datetimepicker";

type DatePickerProps = {
  value: any;
  label: string;
  required?: boolean;
  onChange: any;
};

const DatePicker = ({
  value,
  label,
  required,
  onChange,
  ...props
}: DatePickerProps) => {
  const [showPicker, setShowPicker] = useState(false);

  const onDateChange = (event: any, date?: Date) => {
    setShowPicker(false); // Hide picker after selection
    if (date && onChange) {
      onChange(date); // Update parent state
    }
  };

  return (
    <View>
      {showPicker && (
        <RNDateTimePicker
          maximumDate={new Date()} // Prevent future dates
          value={value || new Date()}
          mode="date"
          display="spinner"
          onChange={onDateChange}
        />
      )}
      <View className={`my-2 w-full`}>
        <Text className={`text-lg font-JakartaSemiBold mb-3 `}>
          {label}
          {required ? <Text className="text-red-700">*</Text> : ""}
        </Text>
        <View
          className={`flex flex-row justify-start items-center relative rounded-md bg-neutral-200 border border-neutral-100 focus:border-primary-500 mt-3`}
        >
          <Pressable onPress={() => setShowPicker(true)} style={{ flex: 1 }}>
            <TextInput
              className="rounded-full p-4 font-JakartaSemiBold text-[15px] flex-1 text-left text-gray-500"
              placeholder="Select Date"
              placeholderTextColor="#88909e"
              editable={false} // Prevent manual input
              value={value ? value.toDateString() : ""}
              {...props}
            />
          </Pressable>
        </View>
      </View>
    </View>
  );
};

export default DatePicker;
