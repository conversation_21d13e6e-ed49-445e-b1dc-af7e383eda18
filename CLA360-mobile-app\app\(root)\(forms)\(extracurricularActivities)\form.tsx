import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
} from "react-native";
import React, { useState } from "react";
import InputField from "../../../../components/InputField";
import { SafeAreaView } from "react-native-safe-area-context";
import Header from "@/components/header";
import { router } from "expo-router";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { PlusCircleIcon, XCircleIcon } from "react-native-heroicons/outline";
import ReactNativeModal from "react-native-modal";
import FormLayout from "@/components/formLayout";
import Radio from "@/components/radio";

type Activity = {
  activity: string;
};

type Volunteer = {
  name: string;
  role: string;
  responsibilities: string;
};

type Award = {
  name: string;
  reason: string;
};

type Talent = {
  talent: string;
};

const Form1 = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [awards, setAwards] = useState<Award[]>([]);
  const [talents, setTalents] = useState<Talent[]>([]);
  const [volunteerWorkOrganizations, setVolunteerWorkOrganizations] = useState<
    Volunteer[]
  >([]);
  const [hasTravelledAbroad, setHasTravelledAbroad] = useState(false);

  const [showActivity, setShowActivity] = useState(false);
  const [showAwards, setShowAwards] = useState(false);
  const [showTalents, setShowTalents] = useState(false);
  const [showVolunteerWork, setShowVolunteerWork] = useState(false);

  const [highSchoolActivities, setHighSchoolActivities] = useState({
    activity: "",
  });
  const [volunteerWorkExperience, setVolunteerWorkExperience] = useState({
    name: "",
    role: "",
    responsibilities: "",
  });
  const [awardData, setAwardData] = useState({
    name: "",
    reason: "",
  });
  const [talentData, setTalentData] = useState({ talent: "" });
  const toggleHasTravelledAbroad = () => {
    let value = hasTravelledAbroad
    setHasTravelledAbroad(!value)
  }

  return (
      <FormLayout title="Extracurricular Activities">
      {/* High School Activites Modal */}
      <ReactNativeModal isVisible={showActivity} onModalHide={() => {}}>
        <View className="flex flex-col bg-white px-5 py-9 rounded-md min-h-[300px]">
          <View className="flex flex-col justify-between items-center">
            <Text className="font-JakartaBold mb-3">
              Add High School Activites
            </Text>
            <InputField
              label="Activity"
              containerStyle="rounded-md mt-0"
              value={highSchoolActivities}
              onChangeText={(value) =>
                setHighSchoolActivities({ activity: value })
              }
            />
          </View>
          <View className="mt-3">
            <TouchableOpacity
              onPress={() => {
                if (!highSchoolActivities.activity) {
                  Alert.alert("Field is Required");
                  return;
                }

                try {
                  setActivities([...activities, highSchoolActivities]);
                  setHighSchoolActivities({
                    activity: "",
                  });
                  setShowActivity(!showActivity);
                } catch (error) {
                  console.log(error);
                }
              }}
              className="bg-blue-400 py-3 rounded-md items-center"
            >
              <Text className="font-JakartaSemiBold">Add</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ReactNativeModal>

      {/* Volunteer Work Modal */}
      <ReactNativeModal isVisible={showVolunteerWork} onModalHide={() => {}}>
        <View className="flex flex-col bg-white px-5 py-9 rounded-md min-h-[300px]">
          <View className="flex flex-col justify-between items-center">
            <Text className="font-JakartaBold mb-3">
              Add Volunteer Experience
            </Text>
            <InputField
              label="Name"
              containerStyle="rounded-md mt-0"
              value={volunteerWorkExperience.name}
              onChangeText={(value) =>
                setVolunteerWorkExperience({
                  ...volunteerWorkExperience,
                  name: value,
                })
              }
            />
            <InputField
              label="Role"
              containerStyle="rounded-md mt-0"
              value={volunteerWorkExperience.role}
              onChangeText={(value) =>
                setVolunteerWorkExperience({
                  ...volunteerWorkExperience,
                  role: value,
                })
              }
            />
            <InputField
              label="Responsibilites"
              containerStyle="rounded-md mt-0"
              value={volunteerWorkExperience.responsibilities}
              onChangeText={(value) =>
                setVolunteerWorkExperience({
                  ...volunteerWorkExperience,
                  responsibilities: value,
                })
              }
            />
          </View>
          <View className="mt-3">
            <TouchableOpacity
              onPress={() => {
                const { name, role, responsibilities } =
                  volunteerWorkExperience;
                if (!name || !role || !responsibilities) {
                  Alert.alert("All fields are required");
                  return;
                }

                try {
                  setVolunteerWorkOrganizations([
                    ...volunteerWorkOrganizations,
                    volunteerWorkExperience,
                  ]);
                  setVolunteerWorkExperience({
                    name: "",
                    role: "",
                    responsibilities: "",
                  });
                  setShowVolunteerWork(!showVolunteerWork);
                } catch (error) {
                  console.log(error);
                }
              }}
              className="bg-blue-400 py-3 rounded-md items-center"
            >
              <Text className="font-JakartaSemiBold">Add</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ReactNativeModal>

      {/* Talents Modal */}
      <ReactNativeModal isVisible={showTalents} onModalHide={() => {}}>
        <View className="flex flex-col bg-white px-5 py-9 rounded-md min-h-[300px]">
          <View className="flex flex-col justify-between items-center">
            <Text className="font-JakartaBold mb-3">Add Talents</Text>
            <InputField
              containerStyle="rounded-md mt-0"
              label="Talent"
              value={talentData.talent}
              onChangeText={(value) => setTalentData({ talent: value })}
            />
          </View>
          <View className="mt-3">
            <TouchableOpacity
              onPress={() => {
                if (!talentData.talent) {
                  Alert.alert("Field is Required");
                  return;
                }

                try {
                  setTalents([...talents, talentData]);
                  setTalentData({
                    talent: "",
                  });
                  setShowTalents(!showTalents);
                } catch (error) {
                  console.log(error);
                }
              }}
              className="bg-blue-400 py-3 rounded-md items-center"
            >
              <Text className="font-JakartaSemiBold">Add</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ReactNativeModal>

      {/* Awards Modal */}
      <ReactNativeModal isVisible={showAwards} onModalHide={() => {}}>
        <View className="flex flex-col bg-white px-5 py-9 rounded-md min-h-[300px]">
          <View className="flex flex-col justify-between items-center">
            <Text className="font-JakartaBold mb-3">Add Awards</Text>
            <InputField
              label="Name"
              containerStyle="rounded-md mt-0"
              value={awardData.name}
              onChangeText={(value) =>
                setAwardData({ ...awardData, name: value })
              }
            />
            <InputField
              label="Reason"
              containerStyle="rounded-md mt-0"
              value={awardData.reason}
              onChangeText={(value) =>
                setAwardData({ ...awardData, reason: value })
              }
            />
          </View>
          <View className="mt-3">
            <TouchableOpacity
              onPress={() => {
                const { name, reason } = awardData;
                if (!name || !reason) {
                  Alert.alert("All fields are required");
                  return;
                }

                try {
                  setAwards([...awards, awardData]);
                  setAwardData({
                    name: "",
                    reason: "",
                  });
                  setShowAwards(!showAwards);
                } catch (error) {
                  console.log(error);
                }
              }}
              className="bg-blue-400 py-3 rounded-md items-center"
            >
              <Text className="font-JakartaSemiBold">Add</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ReactNativeModal>
      
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Extracurricular Activities
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <Text className="text-lg font-JakartaBold mb-3">
          High School Activities
        </Text>
        <View className="w-full flex flex-col">
          <FlatList
            data={activities}
            renderItem={({ item }) => (
              <View className="my-2 flex-1 flex-row items-center justify-between px-5">
                <Text className="font-JakartaBold">{item.activity}</Text>
                <XCircleIcon color="black" />
              </View>
            )}
            ListHeaderComponent={() => (
              <View className="w-full flex flex-col justify-center items-center"></View>
            )}
            ListEmptyComponent={
              <View className="mb-2 flex-1 items-center justify-center">
                <Text className="font-JakartaBold">No Activities</Text>
              </View>
            }
          />
          <View className="items-center justify-end w-full mb-3">
            <TouchableOpacity
              className="py-2 px-3 bg-blue-100 rounded-md"
              onPress={() => {
                setShowActivity(!showActivity);
              }}
            >
              <View className="flex flex-row items-center justify-center gap-2 w-[300px]">
                <PlusCircleIcon color="black" />
                <Text className="font-JakartaSemiBold">Add Activity</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <Text className="text-lg font-JakartaBold mb-3">
          Volunteer Work Organizations
        </Text>
        <View className="w-full flex flex-col">
          <FlatList
            data={volunteerWorkOrganizations}
            renderItem={({ item }) => (
              <View className="flex-1 px-5 mb-2">
                <View className="py-2 flex-1 flex-row items-center justify-between ">
                  <View className="">
                    <Text className="font-JakartaBold">{item.name}</Text>
                    <Text className="font-JakartaSemiBold text-gray-700">
                      {item.responsibilities}
                    </Text>
                  </View>
                  <XCircleIcon color="black" />
                </View>
                <View className="flex-1 h-[2px] bg-gray-300" />
              </View>
            )}
            ListHeaderComponent={() => (
              <View className="w-full flex flex-col justify-center items-center"></View>
            )}
            ListEmptyComponent={
              <View className="mb-2 flex-1 items-center justify-center">
                <Text className="font-JakartaBold">
                  No Volunteer Organizations
                </Text>
              </View>
            }
          />
          <View className="items-center justify-end mb-3">
            <TouchableOpacity
              className="flex flex-row py-2 px-3 items-center justify-center gap-2 bg-blue-100 rounded-md"
              onPress={() => {
                setShowVolunteerWork(!showVolunteerWork);
              }}
            >
              <View className="flex flex-row items-center justify-center gap-2 w-[300px]">
                <PlusCircleIcon color="black" />
                <Text className="font-JakartaSemiBold">Add Organization</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <Text className="text-lg font-JakartaBold mb-3">Awards</Text>
        <View className="w-full flex flex-col">
        <FlatList
            data={awards}
            renderItem={({ item }) => (
              <View className="flex-1 px-5 mb-2">
                <View className="py-2 flex-1 flex-row items-center justify-between ">
                  <View className="">
                    <Text className="font-JakartaBold ">{item.name}</Text>
                    <Text className="font-JakartaSemiBold text-gray-700">
                      {item.reason}
                    </Text>
                  </View>
                  <XCircleIcon color="black" />
                </View>
                <View className="flex-1 h-[2px] bg-gray-300" />
              </View>
            )}
            ListHeaderComponent={() => (
              <View className="w-full flex flex-col justify-center items-center"></View>
            )}
            ListEmptyComponent={
              <View className="mb-2 flex-1 items-center justify-center">
                <Text className="font-JakartaBold">
                  No Awards
                </Text>
              </View>
            }
          />
          <View className="items-center justify-end w-full mb-3">
            <TouchableOpacity
              className="flex flex-row py-2 px-3 items-center justify-center gap-2 bg-blue-100 rounded-md"
              onPress={() => {
                setShowAwards(!showAwards);
              }}
            >
              <View className="flex flex-row items-center justify-center gap-2 w-[300px]">
                <PlusCircleIcon color="black" />
                <Text className="font-JakartaSemiBold">Add Award</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <Text className="text-lg font-JakartaBold mb-3">Talents</Text>
        <View className="w-full flex flex-col">
          <FlatList
            data={talents}
            renderItem={({ item }) => (
              <View className="my-2 flex-1 flex-row items-center justify-between px-5">
                <Text className="font-JakartaBold">{item.talent}</Text>
                <XCircleIcon color="black" />
              </View>
            )}
            ListHeaderComponent={() => (
              <View className="w-full flex flex-col justify-center items-center"></View>
            )}
            ListEmptyComponent={
              <View className="mb-2 flex-1 items-center justify-center">
                <Text className="font-JakartaBold">No Talents</Text>
              </View>
            }
          />
          <View className="items-center justify-end w-full mb-3">
            <TouchableOpacity
              className="flex flex-row py-2 px-3 items-center justify-center gap-2 bg-blue-100 rounded-md"
              onPress={() => {
                setShowTalents(!showTalents);
              }}
            >
              <View className="flex flex-row items-center justify-center gap-2 w-[300px]">
                <PlusCircleIcon color="black" />
                <Text className="font-JakartaSemiBold">Add Talent</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <Radio label="Have You Travelled or Studied Abroad Before?"
        checkedValue={hasTravelledAbroad}
        onChange={toggleHasTravelledAbroad}/>
        

<View className="flex flex-row gap-3 mt-5">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-gray-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">Previous</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {router.push("/(root)/(forms)/(workExperience)/form")}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">Next</Text>
          </TouchableOpacity>
        </View></FormLayout>
  );
};

export default Form1;
