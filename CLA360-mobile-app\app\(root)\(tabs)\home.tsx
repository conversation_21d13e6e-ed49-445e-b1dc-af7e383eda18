import StartApplications from "@/components/StartApplications";
import RequestEvaluation from "@/components/RequestEvaluation";
import RequestTranscript from "@/components/RequestTranscript";
import { icons, images } from "@/constants";
import { SignedIn, SignedOut, useUser } from "@clerk/clerk-expo";
import { Link, router } from "expo-router";
import { Image, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { BellIcon, UserIcon } from "react-native-heroicons/outline"
import { SafeAreaView } from "react-native-safe-area-context";

export default function Page() {
  const { user } = useUser();

  return (
    <SafeAreaView className="bg-white h-full">
      <View className="px-5">
        <View className="w-full  flex flex-row justify-between items-center">
          <Image
            source={images.logo}
            className="w-[70px] h-[50px]"
            resizeMode="contain"
          />
          <View className="flex flex-row justify-center items-center p-2 rounded-full gap-5">
            <TouchableOpacity
              className="rounded-full bg-blue-500 p-2"
              onPress={() => {
                router.push("/(root)/notification");
              }}
            >
              <BellIcon color="white" size={25}/>
            </TouchableOpacity>
            <TouchableOpacity
              className="rounded-full bg-blue-500 p-2"
              onPress={() => {
                router.push("/profile");
              }}
            >
              <UserIcon color="white" size={25}/>
            </TouchableOpacity>
          </View>
        </View>
        <View className="my-5">
          <Text className="font-JakartaSemiBold text-lg text-gray-500">Welcome to Your Student Account</Text>
          <Text className="font-JakartaSemiBold text-sm">Please Complete your profile to start applications</Text>
        </View>
        <View className="flex flex-row justify-between items-center p-0">
          <Text className="font-JakartaSemiBold text-gray-400">Progress:</Text>
          <View className="w-[230px] h-2 bg-gray-200 rounded-full">
            <View className="w-1/2 bg-blue-500 h-full rounded-full" />
          </View>
          <Text className="font-JakartaSemiBold text-gray-400">50%</Text>
        </View>
      </View>
      <ScrollView className="p-10">
        <RequestTranscript />
        <RequestEvaluation />
        <StartApplications />
      </ScrollView>
    </SafeAreaView>
  );
}
