import { View, Text, Alert, TouchableOpacity } from "react-native";
import React, { useEffect, useState } from "react";
import FormLayout from "@/components/formLayout";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import InputField from "@/components/InputField";
import CustomButton from "@/components/customButton";
import {
  PaymentSheetError,
  StripeProvider,
  useStripe,
} from "@stripe/stripe-react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Header from "@/components/header";

const Payment = () => {
  const fetchPublishableKey = async () => {};

  useEffect(() => {
    fetchPublishableKey();
  }, []);

  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  const initializePaymentSheet = async () => {
    const { error } = await initPaymentSheet({
      merchantDisplayName: "College League 360 App",
      intentConfiguration: {
        mode: {
          amount: 500,
          currencyCode: "USD",
        },
        confirmHandler: confirmHandler,
      },
    });
    if (error) {
      // handle error
    }
  };

  useEffect(() => {
    initializePaymentSheet();
  }, []);

  const confirmHandler = async (
    paymentMethod,
    shouldSavePaymentMethod,
    intentCreationCallback
  ) => {
    // explained later
  };

  const onPress = async () => {
    try {
      const { error } = await presentPaymentSheet();

      if (error) {
        if (error.code === PaymentSheetError.Canceled) {
          // Customer canceled - you should probably do nothing.
        } else {
          // PaymentSheet encountered an unrecoverable error. You can display the error to the user, log it, etc.
        }
      } else {
        // Payment completed - show a confirmation screen.
      }
    } catch (err: any) {
      console.error(JSON.stringify(err, null, 2));
    }
  };
  const [promoCode, setPromoCode] = useState("");

  return (
    <StripeProvider
      publishableKey={process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY!}
      merchantIdentifier="merchant.identifier"
      urlScheme="cla360app"
    >
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
      <View className="h-screen bg-white flex flex-col gap-5">
        <View className="flex bg-white mx-10 my-5">
          <Text className="font-JakartaBold text-lg text-gray-600">
            Payment Page
          </Text>
          <Text className="font-JakartaMedium text-md">
            Complete your application payment or apply a promo code for discounts
          </Text>
        </View>
        <View className="mx-10 flex flex-col gap-5 border border-gray-400 py-10 rounded-md shadow-md shadow-neutral-400/70 px-5">
          <View className="">
            <Text className="font-JakartaSemiBold text-gray-700 text-xl">CLA 360 Application Payment</Text>
            <Text>This is the standard payment method</Text>
          </View>
          <Text className="text-sm text-red-500 font-JakartaBold">A $5 charge will be applied for the payment</Text>
          <TouchableOpacity className="w-full bg-blue-500 py-5 items-center justify-center rounded-md" onPress={onPress}>
            <Text className="font-JakartaSemiBold text-white">Make Payment</Text>
          </TouchableOpacity>
          
        </View>
        <View className="mx-10 flex flex-col gap-5 border border-gray-400 py-10 rounded-md px-5">
          <View className="flex flex-col gap-2">
            <Text className="font-JakartaSemiBold text-gray-700 text-xl">Apply Promo Code</Text>
            <Text>
              Enter your promo code below to receive a discount on your payment
            </Text>
          </View>
          <InputField
            label="Promo Code"
            value={promoCode}
            onChangeText={(value) => {
              setPromoCode(value);
            }}
          />
          <TouchableOpacity className="w-full bg-blue-500 py-5 items-center justify-center rounded-md" onPress={onPress}>
            <Text className="font-JakartaSemiBold text-white">Apply Code</Text>
          </TouchableOpacity>
        </View>
      </View>
      </KeyboardAwareScrollView>
    </StripeProvider>
  );
};

export default Payment;
