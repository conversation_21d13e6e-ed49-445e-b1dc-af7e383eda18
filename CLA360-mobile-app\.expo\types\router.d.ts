/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/onboarding` | `/onboarding`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/evaluations` | `/evaluations`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/notification` | `/notification`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/payment` | `/payment`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/profileUpdate` | `/profileUpdate`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/transcripts` | `/transcripts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(additionalInformation)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(educationalDetails)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(extracurricularActivities)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(financialInformation)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(gaurdianDetails)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(personalDetails)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(programPreference)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(transcriptUpload)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(workExperience)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/apply` | `/apply`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/schools` | `/schools`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/search` | `/search`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(universities)'}/apply` | `/apply`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `${'/(root)'}${'/(universities)'}/[id]` | `/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/onboarding` | `/onboarding`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/evaluations` | `/evaluations`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/notification` | `/notification`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/payment` | `/payment`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/profileUpdate` | `/profileUpdate`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/transcripts` | `/transcripts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(additionalInformation)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(educationalDetails)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(extracurricularActivities)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(financialInformation)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(gaurdianDetails)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(personalDetails)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(programPreference)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(transcriptUpload)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(workExperience)'}/form` | `/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/apply` | `/apply`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/schools` | `/schools`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/search` | `/search`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}${'/(universities)'}/apply` | `/apply`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `${'/(root)'}${'/(universities)'}/[id]` | `/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/onboarding${`?${string}` | `#${string}` | ''}` | `/onboarding${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up${`?${string}` | `#${string}` | ''}` | `/sign-up${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/evaluations${`?${string}` | `#${string}` | ''}` | `/evaluations${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/notification${`?${string}` | `#${string}` | ''}` | `/notification${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/payment${`?${string}` | `#${string}` | ''}` | `/payment${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/profileUpdate${`?${string}` | `#${string}` | ''}` | `/profileUpdate${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/transcripts${`?${string}` | `#${string}` | ''}` | `/transcripts${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(additionalInformation)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(educationalDetails)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(extracurricularActivities)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(financialInformation)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(gaurdianDetails)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(personalDetails)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(programPreference)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(transcriptUpload)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(forms)'}${'/(workExperience)'}/form${`?${string}` | `#${string}` | ''}` | `/form${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/apply${`?${string}` | `#${string}` | ''}` | `/apply${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/schools${`?${string}` | `#${string}` | ''}` | `/schools${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(tabs)'}/search${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${'/(universities)'}/apply${`?${string}` | `#${string}` | ''}` | `/apply${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/onboarding` | `/onboarding`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/evaluations` | `/evaluations`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/notification` | `/notification`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/payment` | `/payment`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/profileUpdate` | `/profileUpdate`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/transcripts` | `/transcripts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(additionalInformation)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(educationalDetails)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(extracurricularActivities)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(financialInformation)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(gaurdianDetails)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(personalDetails)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(programPreference)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(transcriptUpload)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(forms)'}${'/(workExperience)'}/form` | `/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/apply` | `/apply`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/schools` | `/schools`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(tabs)'}/search` | `/search`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}${'/(universities)'}/apply` | `/apply`; params?: Router.UnknownInputParams; } | `/+not-found` | `${'/(root)'}${'/(universities)'}/${Router.SingleRoutePart<T>}` | `/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `${'/(root)'}${'/(universities)'}/[id]` | `/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
