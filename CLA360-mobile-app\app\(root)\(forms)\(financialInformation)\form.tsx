import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import InputField from "../../../../components/InputField";
import { router } from "expo-router";
import { enums } from "@/constants";
import FormLayout from "@/components/formLayout";
import SelectDropDown from "@/components/selectDropDown";
import { Picker } from "@react-native-picker/picker";

const FinancialInformation = () => {
  const [financialInformation, setFinancialInformation] = useState({
    financeOption: "",
    annualIncome: "",
    sponsorName: "",
    sponsorRelation: "",
    sponsorEmail: "",
    sponsorPhone: "",
  });

  const [showSponsor, setShowSponsor] = useState(true);
  const financeOption = enums.financialOption;
  const relation = enums.gaurdianRelation;

  return (
    <FormLayout title="Financial Information">
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Financial Details
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <SelectDropDown label="How do you plan to finance your education?">
        <Picker
          style={{ flex: 1 }}
          selectedValue={financialInformation.financeOption}
          onValueChange={(itemValue, itemIndex) =>
            setFinancialInformation({ ...financialInformation, financeOption: itemValue })
          }
        >
          {financeOption?.map((option, index) => (
            <Picker.Item label={option} value={option} key={index} />
          ))}
        </Picker>
      </SelectDropDown>
        <InputField
          label="Annual Income"
          value={financialInformation.annualIncome}
          onChangeText={(value) =>
            setFinancialInformation({
              ...financialInformation,
              annualIncome: value,
            })
          }
        />

        <View
          className={`${
            showSponsor ? "flex" : "h-0"
          } flex-col overflow-hidden transition ease-in`}
        >
            <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Sponsor Information
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
          <InputField
            label="Full Name"
            value={financialInformation.sponsorName}
            onChangeText={(value) =>
              setFinancialInformation({
                ...financialInformation,
                sponsorName: value,
              })
            }
          />
          <InputField
            label="Relation"
            value={financialInformation.sponsorRelation}
            onChangeText={(value) =>
              setFinancialInformation({
                ...financialInformation,
                sponsorRelation: value,
              })
            }
          />
          <InputField
            label="Email"
            value={financialInformation.sponsorEmail}
            onChangeText={(value) =>
              setFinancialInformation({
                ...financialInformation,
                sponsorEmail: value,
              })
            }
          />
          <InputField
            label="Phone"
            type="phone"
            value={financialInformation.sponsorPhone}
            onChangeText={(value) =>
              setFinancialInformation({
                ...financialInformation,
                sponsorPhone: value,
              })
            }
          />
        </View>

        <View className="flex flex-row gap-3 mt-5">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-gray-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">Previous</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {router.push("/(root)/(forms)/(extracurricularActivities)/form")}}
            className="p-3 flex-1 rounded-md flex flex-row justify-center items-center bg-blue-400 mt-3"
          >
            <Text className="font-JakartaSemiBold text-white">Next</Text>
          </TouchableOpacity>
        </View>
    </FormLayout>
  );
};

export default FinancialInformation;
