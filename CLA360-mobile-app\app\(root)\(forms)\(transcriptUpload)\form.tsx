import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import FormLayout from "@/components/formLayout";
import { router } from "expo-router";
import InputField from "@/components/InputField";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import CustomButton from "@/components/customButton";
import * as DocumentPicker from "expo-document-picker";

type File = {
  name: string;
  uri: string;
  type: string;
  size: number;
};

const UploadTranscript = () => {
  const [uploadData, setUploadData] = useState({
    fullName: "",
    indexNumber: "",
    dateOfBirth: new Date(),
    gender: "",
    transcriptType: "",
    file: "",
  });

  const [uploadedFile, setUploadedFile] = useState<File[]>([]);

  const UploadFile = async () => {
    try {
      const docResponse = await DocumentPicker.getDocumentAsync({
        type: "*/*",
      });
      const assets = docResponse.assets;
      if (!assets) return;
      const file = assets[0];

      const fileDetails = {
        name: file.name.split(".")[0],
        uri: file.uri,
        type: file.mimeType,
        size: file.size,
      };
      if (file.size > 4 * 1024 * 1024) {
        alert("File size must be less than 4MB.");
        return;
      }
      console.log(fileDetails);
      setUploadedFile(fileDetails)
    } catch (error) {}
  };

  return (
    <FormLayout title="Upload Transcript">
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Instructions
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <View>
          <Text className="text-[14px] font-JakartaMedium text-justify">
            Please upload a copy of your transcript below. After submitting,
            request your university to send an official copy directly to
            <EMAIL>. Ensure the subject line includes your
            full name and CLA ID.
          </Text>
          <Text className="text-[14px] font-JakartaSemiBold text-justify mt-5">
            This is the Email subject line your school should use:
          </Text>
          <Text className="text-[14px] font-JakartaMedium text-justify mt-2">
            CLA-9226681 - Official Transcript for Gideon
          </Text>
        </View>
        <View className="flex-1 h-[2px] bg-gray-300 my-5" />
        <InputField
          label="Full Name"
          containerStyle="rounded-md"
          value={uploadData.fullName}
          onChangeText={(value) =>
            setUploadData({ ...uploadData, fullName: value })
          }
        />
        <InputField
          label="Index Number"
          containerStyle="rounded-md"
          value={uploadData.indexNumber}
          onChangeText={(value) =>
            setUploadData({ ...uploadData, indexNumber: value })
          }
        />
        <InputField
          label="Date of Birth"
          type="date"
          containerStyle="rounded-md"
          value={uploadData.dateOfBirth}
        />
        <View className="w-full">
          {uploadedFile && (
            <Text className="text-lg mt-2">
              Selected File: {uploadedFile.name}
            </Text>
          )}
          <CustomButton
            bgVariant="outline"
            title="Choose a File"
            onPress={UploadFile}
          />
          <Text className="text-xs">PDF, JPG, JPEG, PNG max. 4mb</Text>
        </View>

        <View className="flex flex-row gap-3">
          <CustomButton bgVariant="success" title="Upload" />
        </View>
      </KeyboardAwareScrollView>
    </FormLayout>
  );
};

export default UploadTranscript;
