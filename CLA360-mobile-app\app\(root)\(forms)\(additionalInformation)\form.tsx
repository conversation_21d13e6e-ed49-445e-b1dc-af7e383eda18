import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import InputField from "@/components/InputField";
import { router } from "expo-router";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import FormLayout from "@/components/formLayout";
import CustomButton from "@/components/customButton";

const AdditionalInformation = () => {
    const [additionalInformation, setAdditionalInformation] = useState({
        personalStatement: "",
        reference1Title: "",
        reference1Name: "",
        reference1Organization: "",
        reference1Email: "",
        reference1Phone: "",
        reference2Title: "",
        reference2Name: "",
        reference2Organization: "",
        reference2Email: "",
        reference2Phone: "",
        hasCriminalRecord: "",
        suspensionReason: "",
        howYouHeardAboutUs: "",
      });

  const [showRewrite, setShowRewrite] = useState(true);

  return (
    <FormLayout title="Additional Information">
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Additional Information
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <InputField
          label="Personal Statement"
          containerStyle="rounded-md"
          type="textarea"
          value={additionalInformation.personalStatement}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              personalStatement: value,
            })
          }
        />
        <View className="flex items-end justify-end">
         <CustomButton bgVariant="outline" title="AI Rewrite" className="w-1/2"/>
        </View>
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Reference 1 Details
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <InputField
          label="Title"
          value={additionalInformation.reference1Title}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference1Title: value,
            })
          }
        />
        <InputField
          label="Name"
          value={additionalInformation.reference1Name}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference1Name: value,
            })
          }
        />
        <InputField
          label="Email"
          value={additionalInformation.reference1Email}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference1Email: value,
            })
          }
        />
        <InputField
          label="Phone"
          value={additionalInformation.reference1Phone}
          type="phone"
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference1Phone: value,
            })
          }
        />
        <InputField
          label="Organization"
          value={additionalInformation.reference1Organization}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference1Organization: value,
            })
          }
        />
        <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <View className="flex-1 h-[2px] bg-gray-300" />
          <Text className="text-lg font-JakartaSemiBold mb-3 text-gray-500">
            Reference 2 Details
          </Text>
          <View className="flex-1 h-[2px] bg-gray-300" />
        </View>
        <InputField
          label="Title"
          value={additionalInformation.reference2Title}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference2Title: value,
            })
          }
        />
        <InputField
          label="Name"
          value={additionalInformation.reference2Name}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference2Name: value,
            })
          }
        />
        <InputField
          label="Email"
          value={additionalInformation.reference2Email}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference2Email: value,
            })
          }
        />
        <InputField
          label="Phone"
          value={additionalInformation.reference2Phone}
          type="phone"
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference2Phone: value,
            })
          }
        />
        <InputField
          label="Organization"
          value={additionalInformation.reference2Organization}
          onChangeText={(value) =>
            setAdditionalInformation({
              ...additionalInformation,
              reference2Organization: value,
            })
          }
        />

        <View className="flex flex-row gap-3 mt-5">
          <CustomButton bgVariant="secondary" title="Previous" onPress={() => {router.back()}}/>
          <CustomButton bgVariant="success" title="Next" onPress={() => {router.back()}}/>
        </View>
    </FormLayout>
  );
};

export default AdditionalInformation;
