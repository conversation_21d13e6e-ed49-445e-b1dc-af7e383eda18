import { StyleSheet, Text, TouchableOpacity, View, Image, Alert, FlatList } from 'react-native'
import React, { useState } from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import Header from '@/components/header'
import { icons, images } from '@/constants'
import { ReactNativeModal } from 'react-native-modal'
import { router } from 'expo-router'
import UniversityCard from '@/components/UniversityCard'

const Transcripts = () => {

  const [showDetails, setShowDetails] = useState(false)

  return (
    <SafeAreaView className="h-screen">
      <Header title="Transcripts"/>
      <View className="w-full p-5 flex items-end justify-end">
        <TouchableOpacity
          onPress={() => {
            router.push("/(root)/(forms)/(transcriptUpload)/form")
          }}
        >
          <View className='flex flex-row items-center gap-2 bg-red-200 rounded-full p-3'>
            <Image source={icons.add} className="w-7 h-7" resizeMode='contain'/>
            <Text className='font-JakartaSemiBold text-gray-600'>Upload Transcript</Text>
          </View>
        </TouchableOpacity>
      </View>
      <FlatList
        data={[]}
        // keyExtractor={(item) => item.id}
        renderItem={({ item }) => <UniversityCard university={item} />}
        ListEmptyComponent={
          <View className="flex flex-col items-center justify-center">
            <Image source={images.noResult} className="w-[150px] h-[150px]" resizeMode="contain"/>
            <Text className="text-lg text-gray-600 font-JakartaBold">No Transcripts Uploaded.</Text>
          </View>
        }
      />
      {/* <ReactNativeModal
          isVisible={showDetails}
          onModalHide={() => {}}
        >
          <View className="bg-white px-7 py-9 rounded-2xl min-h-[300px]">
            <View className='flex flex-row justify-between items-center'>
              <Text className='text-md font-JakartaSemiBold text-gray-700'>Trascript Request</Text>
              <TouchableOpacity onPress={() => {
                setShowDetails(false)
              }}>
                <Image source={icons.close} resizeMode='contain' className='w-[20px] h-[20px]'/>
              </TouchableOpacity>
            </View>
            
          </View>
        </ReactNativeModal> */}
    </SafeAreaView>
  )
}

export default Transcripts