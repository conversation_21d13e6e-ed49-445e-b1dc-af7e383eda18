import React, { useState } from 'react'
import { Image, Text, View, TouchableOpacity, Alert } from "react-native";
import { PencilSquareIcon, UserIcon } from 'react-native-heroicons/outline';
import * as ImagePicker from "expo-image-picker";
import InputField from '@/components/InputField';
import CustomButton from '@/components/customButton';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

const ProfileUpdate = () => {
    const [image, setImage] = useState<string | null>(null);

  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    console.log(result);

    if (!result.canceled) {
      setImage(result.assets[0].uri);
    }
  };

  const SaveUpdate = () => {
    Alert.alert("Confirm Update", "Are you sure you want to change these details?", [{text: "Cancel", onPress: () => {}, style: 'cancel'}, {text: "Ok", onPress: () => {}}])
  }

  const UpdatePassword = () => {
    Alert.alert("Confirm Password Update", "Are you sure you want to Update your password?", [{text: "Cancel", onPress: () => {}, style: 'cancel'}, {text: "Ok", onPress: () => {}}])
  }

  return (
    <View className="h-full bg-white">
      <View className="flex flex-col gap-5 bg-white px-10 py-5">
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">
          <View className='flex flex-col gap-5 w-full  items-center justify-center my-5'>
            {/* Profile Picture */}
            <View>
              {image ? (
                <Image
                  source={{ uri: image }}
                  resizeMode="contain"
                  className="w-[100px] h-[100px] bg-gray-300 rounded-full flex items-center justify-center"
                />
              ) : (
                <View className="w-[100px] h-[100px] bg-gray-300 rounded-full flex items-center justify-center">
                  <UserIcon color="white" size={35} />
                </View>
              )}
              <TouchableOpacity className="absolute bottom-0 right-0" onPress={pickImage}>
              <View className=' '><PencilSquareIcon size="30"/></View>
              </TouchableOpacity></View>
          </View>
          <View className="flex flex-row items-center justify-center gap-2 mt-2">
          <Text className='text-sm font-JakartaBold my-3'>Update Personal Details</Text>
            
            <View className="flex-1 h-[2px] bg-gray-300" />
          </View>
          <View className="flex flex-col gap-1 w-full justify-center items-center border border-blue-500 rounded-md px-3 py-5">
            {/* Details */}
            <InputField 
              label='First Name'
              placeholder='First Name'
            />
            <InputField 
              label='Email'
              placeholder='Email'
            />
            <CustomButton title='Save Update' className='w-full' onPress={SaveUpdate}/>
          </View>
          <View className="flex flex-row items-center justify-center gap-2 mt-2">
            
          <Text className='text-sm font-JakartaBold my-3'>Update Password</Text>
            <View className="flex-1 h-[2px] bg-gray-300" />
          </View>
          <View className="flex flex-col gap-1 w-full justify-center items-center border border-blue-500 rounded-md px-3 py-5 mb-5">
            {/* Details */}
            <InputField 
              label='Password'
              secureTextEntry={true}
              placeholder='*********'
            />
            <InputField 
              label='Confirm Password'
              secureTextEntry={true}
              placeholder='*********'
            />
            <CustomButton title='Update Password' className='w-full' onPress={UpdatePassword}/>
          </View>
        </KeyboardAwareScrollView>
       
      </View>
    </View>
  )
}

export default ProfileUpdate