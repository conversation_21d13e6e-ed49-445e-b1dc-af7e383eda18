import { useLayoutEffect, useState } from "react";
import { Image, ScrollView, Text, View, TouchableOpacity } from "react-native";
import * as ImagePicker from "expo-image-picker";
import { ArrowLeftEndOnRectangleIcon, UserIcon } from "react-native-heroicons/outline";
import MoreItem from "@/components/moreItems";
import { router } from "expo-router";
import Tips from "@/components/tips";

const Profile = () => {
  const [image, setImage] = useState<string | null>(null);

  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    console.log(result);

    if (!result.canceled) {
      setImage(result.assets[0].uri);
    }
  };
  return (
    <View className="bg-white h-full">
      <View className="flex flex-col gap-5 bg-white px-10 py-5">
        <View className="flex flex-col gap-5 w-full  items-center justify-center">
          <View>
            {/* Profile Picture */}
              {image ? (
                <Image
                  source={{ uri: image }}
                  resizeMode="contain"
                  className="w-[100px] h-[100px] bg-gray-300 rounded-full flex items-center justify-center"
                />
              ) : (
                <View className="w-[100px] h-[100px] bg-gray-300 rounded-full flex items-center justify-center">
                  <UserIcon color="white" size={35} />
                </View>
              )}
          </View>
          <View className="flex flex-col gap-1 w-full justify-center items-center">
            {/* Details */}
            <Text className="text-2xl font-JakartaBold text-black">
              Gideon Tang
            </Text>
            <Text className="text-lg font-JakartaBold text-black">
              <EMAIL>
            </Text>
          </View>
        </View>
        <View className="flex flex-row justify-between items-center p-0">
          <Text className="font-JakartaSemiBold text-gray-400">Progress:</Text>
          <View className="w-[230px] h-2 bg-gray-200 rounded-full">
            <View className="w-1/2 bg-blue-500 h-full rounded-full" />
          </View>
          <Text className="font-JakartaSemiBold text-gray-400">50%</Text>
        </View>
      </View>
      <ScrollView className="py-5 px-5 w-full">
        <MoreItem
          title="Notifications"
          onPress={() => {
            router.push("/(root)/(others)/Notification");
          }}
          IconRight={true}
        />
        <MoreItem
          title="College Search"
          onPress={() => {
            router.push("/(root)/(tabs)/search");
          }}
          IconRight={true}
        />
        <MoreItem
          title="Payment"
          onPress={() => {
            router.push("/(root)/payment");
          }}
          IconRight={true}
        />
        <View className="w-full my-3">
          <View className="flex flex-row items-center justify-center gap-2 mt-2">
            <Text className="text-xl font-JakartaBold">Tips and Guides</Text>
            <View className="flex-1 h-[4px] bg-gray-300" />
          </View>
          <View className="w-full m-5 flex flex-col gap-5">
            <Tips
              title="Create an account to access a personalized dashboard"
              checked={true}
            />
            <Tips
              title="Update Profile with a Professional Picture"
              checked={false}
            />
            <Tips
              title="Upload your official Transcripts"
              checked={false}
            />
            <Tips
              title="Fill out the college application form"
              checked={false}
            />
          </View>
        </View>
        <TouchableOpacity onPress={()=> {
        router.replace("/(auth)/sign-in")
      }}>
        <View className="w-full flex flex-row gap-3 px-5 py-5 bg-gray-200 mt-1 items-center justify-center">
         <ArrowLeftEndOnRectangleIcon color="red"/>
            <Text className='text-red-500 font-JakartaSemiBold text-lg'>Log Out</Text>
        </View>
      </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default Profile;
