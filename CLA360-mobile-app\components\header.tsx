import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import { icons } from "@/constants";
import { router } from "expo-router";

type HeaderProps = {
  title: string;
};

const Header = (props: HeaderProps) => {
  return (
    <View className="w-full px-5 my-3">
      <View className="flex flex-row justify-start items-center w-full gap-5">
        <TouchableOpacity
          onPress={()=>{
            router.back()
          }}
        >
          <View className="flex rounded-full bg-blue-300 p-2 items-center">
            <Image
              source={icons.backArrow}
              className="w-7 h-7"
              tintColor="white"
            />
          </View>
        </TouchableOpacity>
        <Text className="text-gray-600 font-JakartaBold">{props.title}</Text>
      </View>
    </View>
  );
};

export default Header;
