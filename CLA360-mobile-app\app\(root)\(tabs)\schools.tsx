import UniversityCard from "@/components/UniversityCard";
import { data, images } from "@/constants";
import { router } from "expo-router";
import { FlatList, Text, View, Image, TouchableOpacity } from "react-native";
import { PlusCircleIcon } from "react-native-heroicons/outline";
import { SafeAreaView } from "react-native-safe-area-context";

const Schools = () => {
  return (
    <View className="h-screen bg-white">
      <FlatList
        data={[]}
        // keyExtractor={(item) => item.id}
        renderItem={({ item }) => <UniversityCard university={item} />}
        ListHeaderComponent={() => (
          <View className="w-full flex flex-col justify-center items-center">
            <View className="flex flex-col gap-1 px-10 pt-5 w-full">
              <Text className="text-2xl font-JakartaBold text-blue-500">
                Track Applications
              </Text>
              <Text className="font-JakartaSemiBold text-gray-500">
                Monitor all your applications at one place
              </Text>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View className="flex flex-col items-center justify-center h-full w-full gap-2">
            <Image
              source={images.noResult}
              className="w-[150px] h-[150px]"
              resizeMode="contain"
            />
            <Text className="text-lg text-gray-600 font-JakartaBold">
              No Applications Started.
            </Text>
            <Text className="text-md text-gray-400 font-JakartaBold">
              We will notify you when schools show interest
            </Text>
          </View>
        }
      />
      <TouchableOpacity
        onPress={() => {
          router.push("/(root)/(forms)/(personalDetails)/form");
        }}
        className="p-3 h-[50px] absolute bottom-56 right-1 rounded-md flex flex-row gap-2 justify-center items-center bg-blue-500 m-5"
      >
          <PlusCircleIcon color="white" />
          <Text className="font-JakartaSemiBold text-gray-100">Start Application</Text>
      </TouchableOpacity>
    </View>
  );
};

export default Schools;
