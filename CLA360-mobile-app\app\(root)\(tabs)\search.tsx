import { View, Text, SafeAreaView, FlatList, TouchableOpacity, Image } from "react-native";
import React, { useState } from "react";
import { data, icons, images } from "@/constants";
import UniversityCard from "@/components/UniversityCard";
import InputField from "@/components/InputField";

const search = () => {
  const [searchTerm, setSearchTerm] = useState("")
  return (
    <SafeAreaView className="bg-white h-screen">
      <FlatList
        data={data.Universities}
        // keyExtractor={(item) => item.id}
        renderItem={({ item }) => <UniversityCard university={item} />}
        ListHeaderComponent={() => (
          <View className="w-full flex flex-col justify-center items-center">
            <View className="flex flex-col gap-1 px-10 pt-5 w-full">
              <Text className="text-2xl font-JakartaBold text-blue-500">
                Schools
              </Text>
              <Text className="font-JakartaSemiBold text-gray-500">
                Begin your search for Schools
              </Text>
              <InputField
            icon={icons.search}
            placeholder="Search"
            value={searchTerm}
            onChangeText={(e) =>
              setSearchTerm(e)
            }
            className="mt-0"
            />
            </View>
            
          </View>
        )}
        ListEmptyComponent={
          <View className="flex flex-col items-center justify-center">
            <Image source={images.noResult} className="w-[150px] h-[150px]" resizeMode="contain"/>
            <Text className="text-lg text-gray-600 font-JakartaBold">No universities found.</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

export default search;
